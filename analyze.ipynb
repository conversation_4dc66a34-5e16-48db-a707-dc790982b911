import pandas as pd
import numpy as np

# 读取数据
df = pd.read_csv("analysis_result_enhanced.csv")
print(f"总数据量: {len(df)} 行")
print(f"胜负分布: {df['win_loss'].value_counts()}")

# 基本过滤：只看获胜的交易，按触发符号计数
win_count = df[df['win_loss'] == 'Win'].groupby("trigger_symbol").agg({'real_rate': 'count'})
print("\n各触发符号的获胜交易数量:")
print(win_count.sort_values('real_rate', ascending=False))

# 2. 详细的过滤后聚合分析
win_trades = df[df['win_loss'] == 'Win']

# 按触发符号的详细统计
trigger_stats = win_trades.groupby('trigger_symbol').agg({
    'real_rate': ['count', 'mean', 'std', 'min', 'max'],
    'log_u_diff_ms': ['mean', 'median', 'std'],
    'u_e_diff_ms': ['mean', 'median', 'std'],
    'quantity': ['sum', 'mean']
}).round(6)

# 扁平化列名
trigger_stats.columns = ['_'.join(col).strip() for col in trigger_stats.columns]
trigger_stats = trigger_stats.sort_values('real_rate_count', ascending=False)

print("按触发符号的详细获胜交易分析:")
print(trigger_stats.head(10))

# 3. 按交易对和方向的聚合分析
pair_direction_stats = win_trades.groupby(['pair_symbol', 'direction']).agg({
    'real_rate': ['count', 'mean'],
    'log_u_diff_ms': 'mean',
    'u_e_diff_ms': 'mean',
    'price': ['min', 'max'],
    'quantity': 'sum'
}).round(6)

pair_direction_stats.columns = ['_'.join(col).strip() for col in pair_direction_stats.columns]
pair_direction_stats = pair_direction_stats.sort_values('real_rate_count', ascending=False)

print("按交易对和方向的获胜交易分析:")
print(pair_direction_stats.head(15))

# 4. 高级过滤：多条件筛选后聚合

# 条件1：高收益率且低延迟的交易
high_performance = df[
    (df['win_loss'] == 'Win') & 
    (df['real_rate'] > 1.0006) & 
    (df['log_u_diff_ms'] < 20) &
    (df['u_e_diff_ms'] < 2)
]

print(f"高性能交易数量: {len(high_performance)}")
if len(high_performance) > 0:
    hp_stats = high_performance.groupby('trigger_symbol').agg({
        'real_rate': ['count', 'mean'],
        'log_u_diff_ms': 'mean',
        'u_e_diff_ms': 'mean'
    }).round(6)
    
    hp_stats.columns = ['_'.join(col).strip() for col in hp_stats.columns]
    print("\n高性能交易按触发符号分析:")
    print(hp_stats)

# 条件2：大额交易分析
large_trades = df[
    (df['win_loss'] == 'Win') & 
    (df['quantity'] > df['quantity'].quantile(0.8))  # 前20%的大额交易
]

print(f"\n大额获胜交易数量: {len(large_trades)}")
if len(large_trades) > 0:
    large_stats = large_trades.groupby('pair_symbol').agg({
        'real_rate': ['count', 'mean'],
        'quantity': ['sum', 'mean'],
        'log_u_diff_ms': 'mean'
    }).round(6)
    
    large_stats.columns = ['_'.join(col).strip() for col in large_stats.columns]
    print("大额获胜交易按交易对分析:")
    print(large_stats.head(10))

# 5. 时间维度的过滤和聚合

# 转换时间列
df['trigger_time'] = pd.to_datetime(df['trigger_log_time'])
df['hour'] = df['trigger_time'].dt.hour
df['minute'] = df['trigger_time'].dt.minute

# 按小时分析获胜交易
hourly_wins = df[df['win_loss'] == 'Win'].groupby('hour').agg({
    'real_rate': ['count', 'mean'],
    'log_u_diff_ms': 'mean',
    'u_e_diff_ms': 'mean'
}).round(6)

hourly_wins.columns = ['_'.join(col).strip() for col in hourly_wins.columns]
print("按小时的获胜交易分析:")
print(hourly_wins)

# 找出最活跃的时间段
most_active_hour = hourly_wins['real_rate_count'].idxmax()
print(f"\n最活跃的小时: {most_active_hour}:00")
print(f"该时段获胜交易数: {hourly_wins.loc[most_active_hour, 'real_rate_count']}")

# 6. 性能排名分析

# 计算每个触发符号的综合性能指标
performance_metrics = win_trades.groupby('trigger_symbol').agg({
    'real_rate': ['count', 'mean', 'std'],
    'log_u_diff_ms': 'mean',
    'u_e_diff_ms': 'mean'
}).round(6)

performance_metrics.columns = ['_'.join(col).strip() for col in performance_metrics.columns]

# 计算综合得分 (收益率高、延迟低、交易次数多)
performance_metrics['profit_score'] = (performance_metrics['real_rate_mean'] - 1) * 10000  # 转换为基点
performance_metrics['latency_score'] = 100 / (performance_metrics['log_u_diff_ms'] + 1)  # 延迟越低得分越高
performance_metrics['volume_score'] = np.log(performance_metrics['real_rate_count'] + 1)  # 交易量得分

# 综合得分 (可以调整权重)
performance_metrics['total_score'] = (
    performance_metrics['profit_score'] * 0.4 + 
    performance_metrics['latency_score'] * 0.3 + 
    performance_metrics['volume_score'] * 0.3
)

# 按综合得分排序
top_performers = performance_metrics.sort_values('total_score', ascending=False)
print("触发符号综合性能排名:")
print(top_performers[['real_rate_count', 'real_rate_mean', 'log_u_diff_ms', 'total_score']].head(10))

# 7. 自定义聚合函数

def profit_analysis(group):
    """自定义聚合函数：计算盈利相关指标"""
    total_trades = len(group)
    avg_profit_rate = group['real_rate'].mean()
    total_profit_points = ((group['real_rate'] - 1) * 10000).sum()  # 总盈利基点
    profit_consistency = 1 / (group['real_rate'].std() + 0.0001)  # 盈利一致性
    avg_latency = group['log_u_diff_ms'].mean()
    
    return pd.Series({
        'total_trades': total_trades,
        'avg_profit_rate': avg_profit_rate,
        'total_profit_points': total_profit_points,
        'profit_consistency': profit_consistency,
        'avg_latency': avg_latency,
        'profit_per_trade': total_profit_points / total_trades if total_trades > 0 else 0
    })

# 应用自定义聚合
custom_analysis = win_trades.groupby('trigger_symbol').apply(profit_analysis).round(4)
custom_analysis = custom_analysis.sort_values('profit_per_trade', ascending=False)

print("自定义盈利分析:")
print(custom_analysis.head(10))

# 8. 总结性洞察

print("=== 数据分析总结 ===")
print(f"总交易数: {len(df)}")
print(f"获胜交易数: {len(win_trades)}")
print(f"胜率: {len(win_trades)/len(df)*100:.2f}%")

print("\n=== 最佳表现者 ===")
best_trigger = top_performers.index[0]
print(f"综合表现最佳触发符号: {best_trigger}")
print(f"该符号获胜交易数: {top_performers.loc[best_trigger, 'real_rate_count']}")
print(f"平均收益率: {top_performers.loc[best_trigger, 'real_rate_mean']:.6f}")
print(f"平均延迟: {top_performers.loc[best_trigger, 'log_u_diff_ms']:.3f}ms")

print("\n=== 交易量统计 ===")
print(f"最活跃的触发符号: {trigger_stats.index[0]} ({trigger_stats.iloc[0]['real_rate_count']} 次获胜交易)")
print(f"平均每个符号获胜交易数: {trigger_stats['real_rate_count'].mean():.1f}")

print("\n=== 延迟统计 ===")
print(f"平均日志延迟: {win_trades['log_u_diff_ms'].mean():.3f}ms")
print(f"平均执行延迟: {win_trades['u_e_diff_ms'].mean():.3f}ms")
print(f"最低延迟交易: {win_trades['log_u_diff_ms'].min():.3f}ms")