#!/usr/bin/env python3
# analyze_arbitrage_log.py
#
# usage: python analyze_arbitrage_log.py 20250715_135408.log records.csv
#
# 作者：ChatGPT o3（根据你的日志格式写的示例脚本）
# 兼容 Python 3.9+

import re
import sys
from pathlib import Path
import csv
from datetime import datetime

try:
    import pandas as pd
except ImportError:
    pd = None  # 没装 pandas 也能跑，只是最后不用 DataFrame

WIN_LOSS_RE = re.compile(r"Wins:\s*(\d+)\s*Losses:\s*(\d+)")
EXPECT_RE   = re.compile(r"Expect(?:ed)? rate:\s*([0-9.]+)")
REAL_RE     = re.compile(r"Real rate:\s*([0-9.]+)")
LAT_RE      = re.compile(
    r"\[(?P<file>[^:]+):\d+] (?P<sym>\w{3,}[\w/]*?) latency:\s*([0-9.]+)\s*us")
SNAP_RE     = re.compile(
    r"orderbook.*bid1=([0-9.]+).*ask1=([0-9.]+).*bid1_size=([0-9.Ee+-]+).*ask1_size=([0-9.Ee+-]+).*e_ts=(\d+).*u_ts=(\d+)",
    re.I)

def parse_blocks(path: Path):
    """Yield one dict per arbitrage attempt."""
    block = {
        "legs": [],  # list of dicts
    }
    prev_wins = prev_losses = None

    with path.open("r", encoding="utf8", errors="ignore") as fh:
        for line in fh:
            # 1) latency 行 → 累积腿
            if m := LAT_RE.search(line):
                block["legs"].append(
                    {
                        "sym": m.group("sym"),
                        "lat_us": float(m.group(3)),
                        "raw": line.strip(),
                    }
                )
                # 捕个粗略时间戳（用第一条腿出现的 log 时间）
                if "start_ts" not in block:
                    try:
                        # 假设行首格式 "2025-07-15 13:54:12.345 ..."
                        ts_str = line[:23]
                        block["start_ts"] = datetime.fromisoformat(ts_str)
                    except Exception:
                        block["start_ts"] = None
                continue

            # 2) expect / real
            if m := EXPECT_RE.search(line):
                block["expect_rate"] = float(m.group(1))
                continue
            if m := REAL_RE.search(line):
                block["real_rate"] = float(m.group(1))
                continue

            # 3) 盘口快照（如果有）
            if m := SNAP_RE.search(line):
                bid1, ask1, bid1_sz, ask1_sz, e_ts, u_ts = m.groups()
                block.setdefault("snapshots", []).append(
                    {
                        "bid1": float(bid1),
                        "ask1": float(ask1),
                        "bid1_sz": float(bid1_sz),
                        "ask1_sz": float(ask1_sz),
                        "e_ts": int(e_ts),
                        "u_ts": int(u_ts),
                    }
                )
                continue

            # 4) wins / losses 行 → 结束一个 block
            if m := WIN_LOSS_RE.search(line):
                wins, losses = map(int, m.groups())
                if prev_wins is None:
                    prev_wins, prev_losses = wins, losses
                    continue  # 第一行基线，不产出记录

                d_win, d_loss = wins - prev_wins, losses - prev_losses
                prev_wins, prev_losses = wins, losses

                # 判定结果
                outcome = None
                if d_win > 0:
                    outcome = "Win"
                elif d_loss > 0:
                    outcome = "Loss"
                else:
                    outcome = "NoTrade"

                # 汇总腿数据
                if block["legs"]:
                    latencies = [leg["lat_us"] for leg in block["legs"]]
                    block["legs_count"] = len(block["legs"])
                    block["max_leg_latency_us"] = max(latencies)
                    block["sum_leg_latency_us"] = sum(latencies)
                else:
                    block["legs_count"] = 0

                block["outcome"] = outcome
                yield block  # --- 输出这条记录

                # 开始下一块
                block = {"legs": []}
                continue

def write_csv(rows, out_path: Path):
    """Flatten dicts → CSV."""
    fieldnames = [
        "start_ts",
        "outcome",
        "legs_count",
        "expect_rate",
        "real_rate",
        "max_leg_latency_us",
        "sum_leg_latency_us",
    ]
    # 动态添加每腿 latency 字段（leg1_lat_us, leg2_lat_us…）
    max_legs = max(r.get("legs_count", 0) for r in rows)
    for i in range(max_legs):
        fieldnames.append(f"leg{i+1}_sym")
        fieldnames.append(f"leg{i+1}_lat_us")

    with out_path.open("w", newline="", encoding="utf8") as csvfh:
        writer = csv.DictWriter(csvfh, fieldnames=fieldnames)
        writer.writeheader()
        for r in rows:
            flat = {k: r.get(k, "") for k in fieldnames}
            for idx, leg in enumerate(r.get("legs", []), 1):
                flat[f"leg{idx}_sym"] = leg["sym"]
                flat[f"leg{idx}_lat_us"] = leg["lat_us"]
            if isinstance(r.get("start_ts"), datetime):
                flat["start_ts"] = r["start_ts"].isoformat(sep=" ")
            writer.writerow(flat)

def main():
    if len(sys.argv) != 3:
        print("usage: python analyze_arbitrage_log.py input.log records.csv")
        sys.exit(1)

    in_file = Path(sys.argv[1])
    out_csv = Path(sys.argv[2])

    records = list(parse_blocks(in_file))
    write_csv(records, out_csv)
    print(f"[+] parsed {len(records)} arbitrage attempts → {out_csv}")

    if pd:
        df = pd.read_csv(out_csv, parse_dates=["start_ts"])
        print(df.head())

if __name__ == "__main__":
    main()
