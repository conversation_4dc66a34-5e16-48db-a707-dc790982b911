# 日志分析脚本使用说明

## 概述

`analyze_fail_logs.py` 是一个用于分析套利引擎fail-logs日志文件的Python脚本。它可以提取每个trigger symbol对应的交易对信息，包括时间戳、价格、数量等详细数据，并计算相关的时间差值。

## 功能特性

- **提取trigger symbol信息**：识别每个套利操作的触发交易对
- **解析交易对数据**：提取价格、数量、方向、时间戳等信息
- **Win/Loss分析**：统计每个操作的成功/失败状态和Real Rate
- **时间差值计算**：
  - `log_time - u_ts`：日志记录时间与u_ts的差值（毫秒）
  - `u_ts - e_ts`：u_ts与e_ts的差值（毫秒）
- **多种输出格式**：支持CSV和JSON格式输出
- **详细统计信息**：包括胜率、各交易对表现等

## 使用方法

### 基本用法

```bash
python3 analyze_fail_logs.py <日志文件路径>
```

### 完整参数

```bash
python3 analyze_fail_logs.py fail-logs/20250715_135408.log \
    --output analysis_result.csv \
    --json analysis_result.json \
    --verbose
```

### 参数说明

- `log_file`：必需，日志文件路径
- `--output, -o`：可选，输出CSV文件路径（默认：analysis_result.csv）
- `--json`：可选，输出JSON文件路径
- `--verbose, -v`：可选，显示详细信息

## 输出文件格式

### CSV文件字段

| 字段名 | 描述 |
|--------|------|
| trigger_symbol | 触发的交易对符号 |
| trigger_log_time | 触发时的日志时间 |
| win_loss | 操作结果（Win/Loss） |
| real_rate | 实际汇率 |
| pair_symbol | 相关交易对符号 |
| pair_log_time | 交易对的日志时间 |
| direction | 交易方向（BUY/SELL） |
| price | 价格 |
| quantity | 数量 |
| u_ts | 原始u_ts时间戳（微秒） |
| e_ts | 原始e_ts时间戳（微秒） |
| from | from字段值 |
| u_ts_formatted | 格式化的u_ts时间 |
| e_ts_formatted | 格式化的e_ts时间 |
| log_u_diff_ms | log_time - u_ts 差值（毫秒） |
| u_e_diff_ms | u_ts - e_ts 差值（毫秒） |

### JSON文件结构

```json
[
  {
    "trigger_symbol": "BNBUSDC",
    "trigger_log_time": "2025-07-15 13:54:09.132054",
    "win_loss": "Win",
    "real_rate": 1.0005505205479452,
    "trading_pairs": [
      {
        "symbol": "BNBUSDC",
        "direction": "SELL",
        "price": 685.18,
        "quantity": 0.021,
        "u_ts": 1752587649131894,
        "e_ts": 1752587649131533,
        "from": 3,
        "log_time": "2025-07-15 13:54:09.132000"
      }
    ],
    "total_pairs": 3
  }
]
```

## 分析结果示例

### 统计摘要

```
=== 日志分析摘要 ===
总共找到 222 个trigger symbol事件

Win/Loss 统计:
  Win: 75 次
  Loss: 76 次
  未知: 71 次
  胜率: 49.67%

Trigger Symbol 统计:
  BTCUSDT: 40 次 (Win: 19, Loss: 7, 胜率: 73.1%)
  ETHUSDT: 26 次 (Win: 6, Loss: 6, 胜率: 50.0%)
  APTUSDT: 14 次 (Win: 6, Loss: 2, 胜率: 75.0%)
  ...
```

### 时间差值分析

- **log_u_diff_ms**：正值表示日志记录时间晚于u_ts，负值表示早于u_ts
- **u_e_diff_ms**：通常为正值，表示u_ts晚于e_ts的时间差

## 注意事项

1. **时间戳格式**：脚本假设时间戳为微秒级别
2. **日志格式**：脚本针对特定的日志格式设计，如果日志格式发生变化可能需要调整正则表达式
3. **内存使用**：对于大型日志文件，脚本会将所有数据加载到内存中
4. **编码**：默认使用UTF-8编码读取文件

## 故障排除

如果遇到解析错误，请检查：
1. 日志文件格式是否正确
2. 文件编码是否为UTF-8
3. 文件路径是否正确
4. Python版本是否支持（建议Python 3.6+）

## 扩展功能

脚本可以根据需要进行扩展，例如：
- 添加更多统计指标
- 支持其他输出格式
- 添加数据可视化功能
- 实现实时日志监控
